import { useState } from 'react';
import { NativeEventEmitter, NativeModules } from 'react-native';
import { CarPlay } from 'react-native-carplay';
import { useDispatch } from 'react-redux';
import BluetoothActions from '../redux/reducers/bluetooth/actions';
import { sendErrorReport } from '../utils/commonFunction';

export const CarPlayStateListener = () => {
  const dispatch = useDispatch();
  const { setCarPlayState } = BluetoothActions;

  useEffect(() => {
    const carPlayEmitter = new NativeEventEmitter(
      NativeModules.CarPlayEventEmitter,
    );

    const fg = carPlayEmitter.addListener('carPlayForeground', () => {
      console.log('✅ ✅ ✅ ✅ CarPlay foreground');
      dispatch(setCarPlayState(true));
      sendErrorReport('true', 'CarPlay_foreground_detected');
    });

    const bg = carPlayEmitter.addListener('carPlayBackground', () => {
      console.log('🎒 🎒 🎒 🎒 CarPlay background');
      dispatch(setCarPlayState(false));
      sendErrorReport('true', 'CarPlay_background_detected');
    });

    return () => {
      fg.remove();
      bg.remove();
    };
  }, [dispatch]);

  return null;
};
