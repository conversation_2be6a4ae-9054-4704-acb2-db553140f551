/**
 * CarPlay Service - Focused on templates, notifications, and business logic
 * State management handled by CarPlayComponent.js
 */

import { CarPlay } from 'react-native-carplay';
import { Platform, NativeEventEmitter, NativeModules } from 'react-native';

class CarPlayService {
  constructor() {
    this.isConnected = false;
    this.currentTemplate = null;
    this.notificationQueue = [];
    this.alertQueue = [];
    // State management removed - handled by CarPlayComponent
  }

  /**
   * Initialize CarPlay connection and set up event listeners
   */
  initializeCarPlay() {
    try {
      // Set up CarPlay connection listeners
      CarPlay.registerOnConnect(() => {
        console.log('CarPlay connected');
        this.isConnected = true;
        // Don't assume foreground state - let CarPlayComponent handle foreground/background
        this.setupInitialTemplate();
        this.processQueuedNotifications();
      });

      CarPlay.registerOnDisconnect(() => {
        console.log('CarPlay disconnected');
        this.isConnected = false;
        // Don't manage foreground state here - CarPlayComponent handles it
        this.currentTemplate = null;
      });

      // Check if already connected
      CarPlay.checkForConnection().then(connected => {
        if (connected) {
          this.isConnected = true;
          // Don't assume foreground state - let CarPlayComponent handle foreground/background
          this.setupInitialTemplate();
        }
      });
    } catch (error) {
      console.error('Error initializing CarPlay:', error);
    }
  }

  /**
   * Set up CarPlay foreground/background event listeners
   * REMOVED: Event handling consolidated to CarPlayComponent for better state management
   */
  // setupCarPlayEventListeners() {
  //   try {
  //     // Set up event emitter for CarPlay events
  //     const { CarPlayEventEmitter } = NativeModules;

  //     if (CarPlayEventEmitter) {
  //       const eventEmitter = new NativeEventEmitter(CarPlayEventEmitter);

  //       // Listen for CarPlay foreground events
  //       eventEmitter.addListener('carPlayForeground', () => {
  //         console.log('CarPlay entered foreground');
  //         this.isCarPlayForeground = true;
  //         this.onCarPlayForeground();
  //       });

  //       // Listen for CarPlay background events
  //       eventEmitter.addListener('carPlayBackground', () => {
  //         console.log('CarPlay entered background');
  //         this.isCarPlayForeground = false;
  //         this.onCarPlayBackground();
  //       });
  //     }
  //   } catch (error) {
  //     console.error('Error setting up CarPlay event listeners:', error);
  //   }
  // }

  /**
   * Called when CarPlay enters foreground
   */
  onCarPlayForeground() {
    console.log('CarPlay is now in foreground - resuming active monitoring');
    // Process any queued notifications
    this.processQueuedNotifications();
    // You can add more foreground-specific logic here
  }

  /**
   * Called when CarPlay enters background
   */
  onCarPlayBackground() {
    console.log('CarPlay is now in background - pausing active monitoring');
    // You can add background-specific logic here
  }

  /**
   * Get current CarPlay status
   * @returns {Object} Status object with connection state
   * Note: Foreground state is now managed by CarPlayComponent
   */
  getCarPlayStatus() {
    return {
      isConnected: this.isConnected,
      hasActiveTemplate: this.currentTemplate !== null,
    };
  }

  /**
   * Set up the initial CarPlay template
   */
  setupInitialTemplate() {
    try {
      const template = {
        type: 'list',
        title: 'ChillBaby',
        sections: [
          {
            header: 'Notifications',
            items: [
              {
                text: 'Recent Alerts',
                detailText: 'View recent baby monitoring alerts',
                onPress: () => this.showRecentAlerts(),
              },
              {
                text: 'Device Status',
                detailText: 'Check connected device status',
                onPress: () => this.showDeviceStatus(),
              },
            ],
          },
        ],
      };

      CarPlay.setRootTemplate(template);
      this.currentTemplate = template;
    } catch (error) {
      console.error('Error setting up CarPlay template:', error);
    }
  }

  /**
   * Show a notification on CarPlay
   * @param {Object} notification - Notification object
   * @param {string} notification.title - Notification title
   * @param {string} notification.message - Notification message
   * @param {string} notification.type - Notification type (info, warning, critical)
   * @param {Function} notification.onPress - Optional callback when notification is pressed
   */
  showNotification(notification) {
    if (!this.isConnected) {
      // Queue notification for when CarPlay connects
      this.notificationQueue.push(notification);
      return;
    }

    try {
      const { title, message, type = 'info', onPress } = notification;

      // Create CarPlay notification
      const carPlayNotification = {
        title: title || 'ChillBaby Alert',
        subtitle: message,
        informativeText: this.getNotificationTypeText(type),
        onPress: onPress || (() => this.showNotificationDetails(notification)),
      };

      // Show banner notification
      CarPlay.showBannerNotification(carPlayNotification);

      console.log('CarPlay notification shown:', title);
    } catch (error) {
      console.error('Error showing CarPlay notification:', error);
    }
  }

  /**
   * Show an alert dialog on CarPlay
   * @param {Object} alert - Alert object
   * @param {string} alert.title - Alert title
   * @param {string} alert.message - Alert message
   * @param {Array} alert.actions - Array of action objects
   */
  showAlert(alert) {
    if (!this.isConnected) {
      // Queue alert for when CarPlay connects
      this.alertQueue.push(alert);
      return;
    }

    try {
      const { title, message, actions = [] } = alert;

      // Create CarPlay alert template
      const alertTemplate = {
        type: 'alert',
        title: title || 'ChillBaby Alert',
        subtitle: message,
        actions: [
          ...actions.map(action => ({
            title: action.title,
            style: action.style || 'default',
            onPress: action.onPress || (() => {}),
          })),
          {
            title: 'Dismiss',
            style: 'cancel',
            onPress: () => this.dismissAlert(),
          },
        ],
      };

      CarPlay.presentTemplate(alertTemplate);
      console.log('CarPlay alert shown:', title);
    } catch (error) {
      console.error('Error showing CarPlay alert:', error);
    }
  }

  /**
   * Show critical baby monitoring alert
   * @param {Object} babyAlert - Baby monitoring alert data
   */
  showBabyAlert(babyAlert) {
    const { type, temperature, humidity, timestamp } = babyAlert;

    let title = 'Baby Monitor Alert';
    let message = '';
    let actions = [];

    switch (type) {
      case 'temperature_high':
        title = '🌡️ High Temperature Alert';
        message = `Temperature: ${temperature}°C - Check baby immediately`;
        actions = [
          {
            title: 'Call Emergency',
            style: 'destructive',
            onPress: () => this.callEmergency(),
          },
          {
            title: 'View Details',
            onPress: () => this.showTemperatureDetails(babyAlert),
          },
        ];
        break;

      case 'temperature_low':
        title = '❄️ Low Temperature Alert';
        message = `Temperature: ${temperature}°C - Baby may be cold`;
        actions = [
          {
            title: 'View Details',
            onPress: () => this.showTemperatureDetails(babyAlert),
          },
        ];
        break;

      case 'humidity_alert':
        title = '💧 Humidity Alert';
        message = `Humidity: ${humidity}% - Check room conditions`;
        actions = [
          {
            title: 'View Details',
            onPress: () => this.showHumidityDetails(babyAlert),
          },
        ];
        break;

      case 'device_disconnected':
        title = '📱 Device Disconnected';
        message = 'Baby monitor lost connection - Check device';
        actions = [
          {
            title: 'Reconnect',
            onPress: () => this.attemptReconnection(),
          },
        ];
        break;

      default:
        message = 'Check baby monitoring app for details';
    }

    // Show as both notification and alert for critical alerts
    this.showNotification({
      title,
      message,
      type: 'critical',
    });

    this.showAlert({
      title,
      message,
      actions,
    });
  }

  /**
   * Process queued notifications when CarPlay connects
   */
  processQueuedNotifications() {
    // Process queued notifications
    while (this.notificationQueue.length > 0) {
      const notification = this.notificationQueue.shift();
      this.showNotification(notification);
    }

    // Process queued alerts
    while (this.alertQueue.length > 0) {
      const alert = this.alertQueue.shift();
      this.showAlert(alert);
    }
  }

  /**
   * Get notification type text for display
   */
  getNotificationTypeText(type) {
    switch (type) {
      case 'critical':
        return '🚨 Critical Alert';
      case 'warning':
        return '⚠️ Warning';
      case 'info':
      default:
        return 'ℹ️ Information';
    }
  }

  /**
   * Show recent alerts in CarPlay
   */
  showRecentAlerts() {
    // This would integrate with your existing alert system
    console.log('Showing recent alerts in CarPlay');
    // Implementation would fetch recent alerts and display them
  }

  /**
   * Show device status in CarPlay
   */
  showDeviceStatus() {
    // This would integrate with your existing device status system
    console.log('Showing device status in CarPlay');
    // Implementation would fetch device status and display it
  }

  /**
   * Show notification details
   */
  showNotificationDetails(notification) {
    console.log('Showing notification details:', notification);
    // Implementation would show detailed view of notification
  }

  /**
   * Show temperature details
   */
  showTemperatureDetails(alert) {
    console.log('Showing temperature details:', alert);
    // Implementation would show temperature trend and details
  }

  /**
   * Show humidity details
   */
  showHumidityDetails(alert) {
    console.log('Showing humidity details:', alert);
    // Implementation would show humidity trend and details
  }

  /**
   * Call emergency services
   */
  callEmergency() {
    console.log('Emergency call initiated from CarPlay');
    // Implementation would initiate emergency call
  }

  /**
   * Attempt device reconnection
   */
  attemptReconnection() {
    console.log('Attempting device reconnection from CarPlay');
    // Implementation would attempt to reconnect to baby monitor
  }

  /**
   * Dismiss current alert
   */
  dismissAlert() {
    try {
      CarPlay.dismissTemplate();
    } catch (error) {
      console.error('Error dismissing CarPlay alert:', error);
    }
  }

  /**
   * Check if CarPlay is connected
   */
  isCarPlayConnected() {
    return this.isConnected;
  }

  /**
   * Cleanup CarPlay service
   */
  cleanup() {
    try {
      if (this.isConnected) {
        CarPlay.disconnect();
      }
      this.isConnected = false;
      this.currentTemplate = null;
      this.notificationQueue = [];
      this.alertQueue = [];
    } catch (error) {
      console.error('Error cleaning up CarPlay service:', error);
    }
  }
}

// Export singleton instance
export default new CarPlayService();
