import React, { useEffect, useRef, useState, useCallback } from 'react';
import { NativeEventEmitter, NativeModules } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import messaging from '@react-native-firebase/messaging';
import {
  CarPlay,
  AlertTemplate,
  ActionSheetTemplate,
  InformationTemplate,
} from 'react-native-carplay';
import { sendErrorReport } from '../utils/commonFunction';

// Safe destructuring to avoid undefined errors
const MaxRCTCarPlayNotificationManager =
  NativeModules.MaxRCTCarPlayNotificationManager || null;

if (!MaxRCTCarPlayNotificationManager) {
  console.log(
    'Available modules:',
    Object.keys(NativeModules).filter(key => key.includes('CarPlay')),
  );
}

const CarPlayNotificationHandler = () => {
  const dispatch = useDispatch();
  const { alertData } = useSelector(state => state.bluetooth);
  const carplayState = useSelector(state => state.bluetooth.carplayState);
  const carplayStateRef = useRef(carplayState);
  const pendingNotification = useRef(null);
  const [carPlayConnected, setCarPlayConnected] = useState(CarPlay.connected);

  /** Keep carplayState stable via ref */
  useEffect(() => {
    carplayStateRef.current = carplayState;
  }, [carplayState]);

  useEffect(() => {
    // Check and sync CarPlay connection status on component mount
    const checkCarPlayConnection = () => {
      if (CarPlay.connected !== carPlayConnected) {
        setCarPlayConnected(CarPlay.connected);
        sendErrorReport(CarPlay.connected, 'carplay_connected');

        // If CarPlay is connected and we have a pending notification, show it
        if (CarPlay.connected && pendingNotification.current) {
          handleNotification(pendingNotification.current);
          pendingNotification.current = null;
        }
      }
    };

    // Check immediately
    checkCarPlayConnection();

    // Also trigger the native check for connection
    if (CarPlay.bridge && CarPlay.bridge.checkForConnection) {
      CarPlay.bridge.checkForConnection();
    }
  }, []); // Empty dependency array - only run on mount

  // Add this useEffect to monitor state changes
  useEffect(() => {
    // If there's a mismatch, log it
    if (CarPlay.connected !== carPlayConnected) {
      console.warn('⚠️ State mismatch detected!');
      console.log('  - Native says:', CarPlay.connected);
      console.log('  - State says:', carPlayConnected);
    }
  }, [carPlayConnected]);

  // Connection handlers
  const onConnect = useCallback(() => {
    sendErrorReport('true', 'carplay_connected_onconnect');
    setCarPlayConnected(true);

    if (pendingNotification.current) {
      // Use setTimeout to ensure state has updated
      setTimeout(() => {
        handleNotification(pendingNotification.current);
        pendingNotification.current = null;
      }, 100);
    }
  }, []); // Remove handleNotification dependency to avoid stale closure

  const onDisconnect = useCallback(() => {
    sendErrorReport('false', 'carplay_connected_onDisconnect');
    setCarPlayConnected(false);
  }, []); // Remove handleNotification dependency to avoid stale closure

  /** Core: show CarPlay alert if connected */
  const handleNotification = useCallback(notification => {
    console.log('🚀 ~ CarPlayComponent ~ notification:', notification);
    console.log(
      '🟢 🟢 🟢 🟢  ~ CarPlayComponent ~ notification:',
      carplayStateRef.current,
    );

    if (!carplayStateRef.current) {
      if (MaxRCTCarPlayNotificationManager) {
        MaxRCTCarPlayNotificationManager.sendCarPlayNotification(
          title || 'Notification',
          message,
        )
          .then(res => {
            sendErrorReport(res, 'carplay_banner_send');
            console.log('✅ CarPlay notification result:', res);
          })
          .catch(err => {
            sendErrorReport(err, 'carplay_banner_send_error');
            console.error('❌ Error sending CarPlay notification:', err);
          });
      } else {
        console.warn('❌ CarPlay notification manager not available');
      }
      return;
    }

    try {
      const alertTemplate = new AlertTemplate({
        titleVariants: [notification.message || ''],
        actions:
          notification.actions?.length > 0
            ? notification.actions
            : [{ id: 'ok', title: 'OK' }],
        onActionButtonPressed: ({ id }) => {
          CarPlay.dismissTemplate();
          console.log('CarPlay alert action pressed', id);
        },
      });

      CarPlay.presentTemplate(alertTemplate, true);
      console.log('✅ AlertTemplate presented successfully');
      sendErrorReport(notification, 'carplay_notification_alert');
    } catch (e) {
      console.log('CarPlay presentTemplate error', e);
    }
  }, []);

  /** Listen to Firebase push notifications */
  useEffect(() => {
    const unsub = messaging().onMessage(async remoteMessage => {
      if (remoteMessage?.data) {
        const { title, message, type } = remoteMessage.data;
        const notification = {
          title,
          message,
          type,
          actions: [],
        };
        sendErrorReport(notification, 'alertData_carplay');
        handleNotification(notification);
      }
    });
    return unsub;
  }, [handleNotification]);

  /** When Redux alertData changes */
  useEffect(() => {
    if (alertData && Object.keys(alertData).length > 0) {
      const notification = {
        title: alertData.title || '',
        message: alertData.message || '',
        type: alertData.type || '',
        actions: alertData.actions || [],
      };
      sendErrorReport(notification, 'carplay_notification');
      handleNotification(notification);
    }
  }, [alertData, handleNotification]);

  // Set up CarPlay event listeners
  useEffect(() => {
    const emt = new NativeEventEmitter(NativeModules.RNCarPlay);
    const connectListener = emt.addListener('didConnect', onConnect);

    // Set up notification listener
    const notificationEmitter = new NativeEventEmitter(
      NativeModules.MaxRCTCarPlayNotificationManager,
    );

    const notificationListener = notificationEmitter.addListener(
      'carPlayNotification',
      notification => {
        handleNotification(notification);
      },
    );

    CarPlay.registerOnConnect(onConnect);
    CarPlay.registerOnDisconnect(onDisconnect);

    return () => {
      connectListener.remove();
      notificationListener?.remove();
      CarPlay.unregisterOnConnect(onConnect);
      CarPlay.unregisterOnDisconnect(onDisconnect);
    };
  }, [onConnect, onDisconnect]);

  return <View></View>;
};

export default CarPlayNotificationHandler;
