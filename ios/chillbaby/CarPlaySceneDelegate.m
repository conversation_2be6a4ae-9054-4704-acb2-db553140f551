#import "CarPlaySceneDelegate.h"
#import <React/RCTBridge.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <React/RCTEventDispatcher.h>
#import "RNCarPlay.h"
#import "CarPlayEventEmitter.h"
#import "MaxRCTCarPlayNotificationManager.h"
#import "AppDelegate.h"

@implementation CarPlaySceneDelegate

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
           didConnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay connected - interface controller received");

    self.interfaceController = interfaceController;
    self.isCarPlayConnected = YES; // store state

    // Tell react-native-carplay CarPlay is connected
    [RNCarPlay connectWithInterfaceController:interfaceController window:templateApplicationScene.carWindow];

    // Notify MaxRCTCarPlayNotificationManager about the interface controller
    AppDelegate *appDelegate = (AppDelegate *)UIApplication.sharedApplication.delegate;
    RCTBridge *bridge = appDelegate.bridge;
    if (bridge != nil) {
        MaxRCTCarPlayNotificationManager *notificationManager = [bridge moduleForClass:[MaxRCTCarPlayNotificationManager class]];
        if (notificationManager != nil) {
            [notificationManager setInterfaceController:interfaceController];
            NSLog(@"✅ CarPlay interface controller set in notification manager");
        } else {
            NSLog(@"⚠️ MaxRCTCarPlayNotificationManager not found");
        }
    } else {
        NSLog(@"⚠️ RN bridge not ready when setting interface controller");
    }
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
        didDisconnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay disconnected");

    // Clear the interface controller
    self.interfaceController = nil;
    self.isCarPlayConnected = NO; // store state

    // Notify React Native that CarPlay is disconnected
    [RNCarPlay disconnect];

    // Clear interface controller from MaxRCTCarPlayNotificationManager
    AppDelegate *appDelegate = (AppDelegate *)UIApplication.sharedApplication.delegate;
    RCTBridge *bridge = appDelegate.bridge;
    if (bridge != nil) {
        MaxRCTCarPlayNotificationManager *notificationManager = [bridge moduleForClass:[MaxRCTCarPlayNotificationManager class]];
        if (notificationManager != nil) {
            [notificationManager clearInterfaceController];
            NSLog(@"✅ CarPlay interface controller cleared from notification manager");
        } else {
            NSLog(@"⚠️ MaxRCTCarPlayNotificationManager not found during disconnect");
        }
    } else {
        NSLog(@"⚠️ RN bridge not ready when clearing interface controller");
    }
}

// MARK: Foreground / Background
- (void)sceneDidBecomeActive:(UIScene *)scene {
    NSLog(@"CarPlay scene became active");
    [self sendCarPlayEvent:@"carPlayForeground"];
}

- (void)sceneWillResignActive:(UIScene *)scene {
    NSLog(@"CarPlay scene will resign active");
    [self sendCarPlayEvent:@"carPlayBackground"];
}

// Keep the old methods for compatibility but don't send events
- (void)sceneWillEnterForeground:(UIScene *)scene {
    NSLog(@"CarPlay scene will enter foreground (not sending event)");
}

- (void)sceneDidEnterBackground:(UIScene *)scene {
    NSLog(@"CarPlay scene did enter background (not sending event)");
}

- (void)sendCarPlayEvent:(NSString *)name {
    AppDelegate *appDelegate = (AppDelegate *)UIApplication.sharedApplication.delegate;
    RCTBridge *bridge = appDelegate.bridge;   // ✅ use existing RN bridge
    if (bridge != nil) {
        CarPlayEventEmitter *emitter = [bridge moduleForClass:[CarPlayEventEmitter class]];
        if (emitter != nil) {
            [emitter sendEventWithName:name body:@{}];
        } else {
            NSLog(@"⚠️ CarPlayEventEmitter not found");
        }
    } else {
        NSLog(@"⚠️ RN bridge not ready yet");
    }
}



@end
